import { glob } from 'glob';
import { Project, SyntaxKind } from 'ts-morph';

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run') || args.includes('-d');

console.log(
  `🔍 LoadChildren Routes Transformation Tool ${isDryRun ? '(DRY RUN MODE)' : ''}`,
);
console.log('=================================================\n');

// Create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
  skipAddingFilesFromTsConfig: true,
});

// Step 1: Find all routes files within apps/fincloud directory
console.log('🔎 Discovering routes files...');
const routesFiles = glob.sync(['apps/fincloud/**/*.routes.ts'], {
  ignore: ['node_modules/**', 'dist/**', 'coverage/**', '.git/**'],
});

console.log(`📁 Found ${routesFiles.length} routes files\n`);

// Step 2: Parse and modify each routes file
let modifiedFiles = 0;
let totalTransformations = 0;
const modifiedFilesList = [];

for (const routesFile of routesFiles) {
  try {
    const sourceFile = project.addSourceFileAtPath(routesFile);
    let fileModified = false;
    let transformationsInFile = 0;

    // Find the ROUTES constant declaration
    const variableStatements = sourceFile.getVariableStatements();

    for (const variableStatement of variableStatements) {
      const declarations = variableStatement.getDeclarations();

      for (const declaration of declarations) {
        const name = declaration.getName();

        if (name === 'ROUTES') {
          const initializer = declaration.getInitializer();

          if (
            initializer &&
            initializer.getKind() === SyntaxKind.ArrayLiteralExpression
          ) {
            const routesArray = initializer;

            // Recursively process route objects
            const processRouteObject = (routeObject) => {
              if (
                routeObject.getKind() === SyntaxKind.ObjectLiteralExpression
              ) {
                const properties = routeObject.getProperties();

                for (const property of properties) {
                  if (property.getKind() === SyntaxKind.PropertyAssignment) {
                    const propertyAssignment = property;
                    const nameNode = propertyAssignment.getNameNode();

                    // Check for loadChildren property
                    if (nameNode && nameNode.getText() === 'loadChildren') {
                      const initializer = propertyAssignment.getInitializer();

                      if (
                        initializer &&
                        initializer.getKind() === SyntaxKind.ArrowFunction
                      ) {
                        const arrowFunction = initializer;
                        const body = arrowFunction.getBody();

                        // The body could be a direct call expression or a more complex structure
                        const processCallExpression = (callExpr) => {
                          if (
                            callExpr.getKind() === SyntaxKind.CallExpression
                          ) {
                            const expression = callExpr.getExpression();

                            // Check if it's a .then() call on an import()
                            if (
                              expression &&
                              expression.getKind() ===
                                SyntaxKind.PropertyAccessExpression
                            ) {
                              const propAccess = expression;
                              const propName = propAccess.getName();

                              if (propName === 'then') {
                                const leftSide = propAccess.getExpression();

                                // Check if the left side is an import() call
                                if (
                                  leftSide &&
                                  leftSide.getKind() ===
                                    SyntaxKind.CallExpression
                                ) {
                                  const importCall = leftSide;
                                  const importExpression =
                                    importCall.getExpression();

                                  if (
                                    importExpression &&
                                    importExpression.getKind() ===
                                      SyntaxKind.ImportKeyword
                                  ) {
                                    const importArgs =
                                      importCall.getArguments();

                                    if (importArgs.length > 0) {
                                      const importPath = importArgs[0];
                                      const importPathText =
                                        importPath.getText();

                                      // Check if the import path contains .module
                                      if (importPathText.includes('.module')) {
                                        console.log(
                                          `  🎯 Found loadChildren with module import in: ${routesFile}`,
                                        );
                                        console.log(
                                          `    📄 Import path: ${importPathText}`,
                                        );

                                        if (!isDryRun) {
                                          // Transform the import path from .module to .routes
                                          const newImportPath =
                                            importPathText.replace(
                                              /\.module(['"])/g,
                                              '.routes$1',
                                            );
                                          importPath.replaceWithText(
                                            newImportPath,
                                          );
                                          console.log(
                                            `    ✏️  Updated import path to: ${newImportPath}`,
                                          );

                                          // Find and transform the .then() callback
                                          const thenArgs =
                                            callExpr.getArguments();
                                          if (thenArgs.length > 0) {
                                            const callback = thenArgs[0];
                                            if (
                                              callback.getKind() ===
                                              SyntaxKind.ArrowFunction
                                            ) {
                                              const callbackBody =
                                                callback.getBody();
                                              if (
                                                callbackBody &&
                                                callbackBody.getKind() ===
                                                  SyntaxKind.PropertyAccessExpression
                                              ) {
                                                const propertyAccess =
                                                  callbackBody;
                                                const propertyName =
                                                  propertyAccess.getName();

                                                // Check if it's accessing a module class (ends with 'Module')
                                                if (
                                                  propertyName.endsWith(
                                                    'Module',
                                                  )
                                                ) {
                                                  console.log(
                                                    `    📝 Found module reference: ${propertyName}`,
                                                  );
                                                  propertyAccess
                                                    .getNameNode()
                                                    .replaceWithText('ROUTES');
                                                  console.log(
                                                    `    ✏️  Updated callback to return: ROUTES`,
                                                  );
                                                }
                                              }
                                            }
                                          }
                                        } else {
                                          // In dry run mode, just log what would be changed
                                          const newImportPath =
                                            importPathText.replace(
                                              /\.module(['"])/g,
                                              '.routes$1',
                                            );
                                          console.log(
                                            `    🔍 Would update import path to: ${newImportPath}`,
                                          );
                                          console.log(
                                            `    🔍 Would update callback to return: ROUTES`,
                                          );
                                        }

                                        transformationsInFile++;
                                        fileModified = true;
                                      }
                                    }
                                  }
                                }
                              }
                            }
                            // Also check if it's a direct import() call
                            else if (
                              expression &&
                              expression.getKind() === SyntaxKind.ImportKeyword
                            ) {
                              const args = callExpr.getArguments();

                              if (args.length > 0) {
                                const importPath = args[0];
                                const importPathText = importPath.getText();

                                // Check if the import path contains .module
                                if (importPathText.includes('.module')) {
                                  console.log(
                                    `  🎯 Found loadChildren with module import in: ${routesFile}`,
                                  );
                                  console.log(
                                    `    📄 Import path: ${importPathText}`,
                                  );

                                  if (!isDryRun) {
                                    // Transform the import path from .module to .routes
                                    const newImportPath =
                                      importPathText.replace(
                                        /\.module(['"])/g,
                                        '.routes$1',
                                      );
                                    importPath.replaceWithText(newImportPath);
                                    console.log(
                                      `    ✏️  Updated import path to: ${newImportPath}`,
                                    );
                                  } else {
                                    // In dry run mode, just log what would be changed
                                    const newImportPath =
                                      importPathText.replace(
                                        /\.module(['"])/g,
                                        '.routes$1',
                                      );
                                    console.log(
                                      `    🔍 Would update import path to: ${newImportPath}`,
                                    );
                                  }

                                  transformationsInFile++;
                                  fileModified = true;
                                }
                              }
                            }
                          }
                        };

                        if (body) {
                          processCallExpression(body);
                        }
                      }
                    }

                    // Check for children property to recursively process nested routes
                    if (nameNode && nameNode.getText() === 'children') {
                      const childrenInitializer =
                        propertyAssignment.getInitializer();
                      if (
                        childrenInitializer &&
                        childrenInitializer.getKind() ===
                          SyntaxKind.ArrayLiteralExpression
                      ) {
                        const childrenArray = childrenInitializer;
                        const childElements = childrenArray.getElements();

                        for (const childElement of childElements) {
                          processRouteObject(childElement);
                        }
                      }
                    }
                  }
                }
              }
            };

            // Process all route objects in the ROUTES array
            const elements = routesArray.getElements();
            for (const element of elements) {
              processRouteObject(element);
            }
          }
        }
      }
    }

    // Save the file if it was modified
    if (fileModified) {
      if (!isDryRun) {
        sourceFile.saveSync();
        console.log(`  ✅ Modified and saved: ${routesFile}\n`);
      } else {
        console.log(`  🔍 Would modify: ${routesFile}\n`);
      }

      modifiedFiles++;
      totalTransformations += transformationsInFile;
      modifiedFilesList.push({
        file: routesFile,
        transformationCount: transformationsInFile,
      });
    }
  } catch (error) {
    console.log(`  ❌ Error processing ${routesFile}: ${error.message}`);
    // Skip files that can't be processed
  }
}

// Output summary
console.log('\n📊 SUMMARY');
console.log('===========');
console.log(
  `${isDryRun ? 'Files that would be modified' : 'Modified files'}: ${modifiedFiles}`,
);
console.log(
  `${isDryRun ? 'LoadChildren transformations that would be applied' : 'Total loadChildren transformations applied'}: ${totalTransformations}`,
);

if (modifiedFilesList.length > 0) {
  console.log('\n📋 DETAILED RESULTS:');
  modifiedFilesList.forEach(({ file, transformationCount }) => {
    console.log(
      `  • ${file} (${transformationCount} transformation${transformationCount > 1 ? 's' : ''})`,
    );
  });
}

if (isDryRun) {
  console.log(
    '\n💡 To apply these changes, run the script without --dry-run flag',
  );
} else if (modifiedFiles > 0) {
  console.log('\n✨ All changes have been applied successfully!');
  console.log(
    '💡 Consider running your linter and tests to ensure everything is working correctly.',
  );
} else {
  console.log(
    '\n🎉 No loadChildren with module imports found in routes files.',
  );
}
