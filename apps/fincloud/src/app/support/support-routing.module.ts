import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';




const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),
    children: [
      {
        path: 'cancelled',
        loadComponent: () => import('./components/cancelled-invitation/cancelled-invitation.component').then(m => m.CancelledInvitationComponent),
      },
      {
        path: 'support',
        loadComponent: () => import('./components/contact-support/contact-support.component').then(m => m.ContactSupportComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [],
})
export class SupportRoutingModule {}
