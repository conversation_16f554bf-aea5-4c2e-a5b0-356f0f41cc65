import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

import { IconComponent } from '@fincloud/components/icons';
import { TableColumn } from '@fincloud/components/lists';
import {
  BUSINESS_CASE_LEADER_COLUMN_CONFIG,
  UserListTableComponent,
} from '@fincloud/neoshare/business-case';
import {
  selectAutoGeneratedBusinessCaseName,
  selectCaseParticipantsFromMyOrganization,
} from '@fincloud/state/business-case';
import { Store } from '@ngrx/store';

@Component({
  selector: 'app-restricted-case-access',
  templateUrl: './restricted-case-access.component.html',
  styleUrls: ['./restricted-case-access.component.scss'],
  imports: [CommonModule, IconComponent, UserListTableComponent],
})
export class RestrictedCaseAccessComponent {
  columns: TableColumn[] = BUSINESS_CASE_LEADER_COLUMN_CONFIG;
  rows$ = this.store.select(selectCaseParticipantsFromMyOrganization);

  autoGeneratedBusinessCaseName$ = this.store.select(
    selectAutoGeneratedBusinessCaseName,
  );

  constructor(private store: Store) {}
}
