import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';

import { casesGuard } from './guards/cases.guard';

const routes: Routes = [
  {
    path: '',
    canActivate: [casesGuard],
    loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),
    children: [
      {
        path: 'my-cases',
        canActivate: [casesGuard],
        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),
      },
      {
        path: 'customer-cases',
        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),
      },
      {
        path: 'applications-invitations',
        canActivate: [casesGuard],
        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),
      },
      {
        path: 'invitations',
        canActivate: [casesGuard],
        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [],
})
export class CasesRoutingModule {}
