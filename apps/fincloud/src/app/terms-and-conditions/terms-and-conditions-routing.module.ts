import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';



const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('./components/accept-terms/accept-terms.component').then(m => m.AcceptTermsComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [],
})
export class TermsAndConditionsRoutingModule {}
