import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';

import { companyGraphGuard } from './guards/company-graph.guard';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/company-graph-container/company-graph-container.component').then(m => m.CompanyGraphContainerComponent),
    canActivate: [companyGraphGuard],
  },
];

@NgModule({
  imports: [],
  exports: [],
})
export class CompanyGraphRoutingModule {}
