import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { KpiMetric, KpiMetricListComponent } from '@fincloud/components/lists';
import { PointOfInterest } from '@fincloud/core/location';

import {
  AzureMapPopupComponent,
  DashboardMapComponent,
} from '@fincloud/components/azure-map';

import {
  SelectComponent,
  SelectComponent as SelectComponent_1,
} from '@fincloud/components/selects';

import { DotsLoaderComponent } from '@fincloud/components/dots-loader';
import { ExecuteFuncPipe } from '@fincloud/core/pipes';
import { findLocationPopup } from '@fincloud/core/utils';

@Component({
  selector: 'app-real-estate-kpi-location-overview-widget',
  templateUrl: './real-estate-kpi-location-overview-widget.component.html',
  styleUrls: ['./real-estate-kpi-location-overview-widget.component.scss'],
  imports: [
    DashboardMapComponent,
    SelectComponent_1,
    DotsLoaderComponent,
    AzureMapPopupComponent,
    ExecuteFuncPipe,
    KpiMetricListComponent,
  ],
})
export class RealEstateKpiLocationOverviewWidgetComponent implements OnChanges {
  @ViewChild(SelectComponent) selectComponent: SelectComponent;

  @Input() kpis: KpiMetric[][] = [];
  @Input() mapLocations: PointOfInterest[] = [];
  @Input() customerKey: string;
  @Input() activePopupId: string;

  locationCoordinates: [number, number];
  pointsOfInterest: PointOfInterest[] = [];
  findLocationPopup = findLocationPopup;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.mapLocations?.currentValue?.length) {
      this.selectComponent?.clearSelect();
      this.pointsOfInterest = this.mapLocations;
    }

    if (
      changes.mapLocations?.previousValue?.length &&
      !changes.mapLocations?.currentValue?.length
    ) {
      this.pointsOfInterest = [];
      this.selectComponent?.clearSelect();
    }
  }

  onLocationChange(location: PointOfInterest) {
    if (location) {
      this.locationCoordinates = [+location.longitude, +location.latitude];
    }
  }
}
