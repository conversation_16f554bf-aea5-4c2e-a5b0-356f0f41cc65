import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';

import { businessCaseKpisListGuard } from './guards/business-case-kpi-list.guard.guard';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/business-case-kpi/business-case-kpi.component').then(m => m.BusinessCaseKpiComponent),
    canActivate: [businessCaseKpisListGuard],
  },
];

@NgModule({
  imports: [],
  exports: [],
})
export class BusinessCaseKpiRoutingModule {}
